"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { ChevronDownIcon, ChevronUpIcon, GearIcon, LinkIcon, ArrowLeftIcon, BriefcaseIcon, CrownIcon, StarIcon, UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, GlobeIcon, CalendarIcon, BuildingIcon, UsersIcon, DollarSignIcon, RocketIcon, ChartLineIcon } from "@ui/components/icons/FontAwesomeRegular"
import { ExternalLinkIcon } from "@radix-ui/react-icons"
import { HandThumbUpIcon, HandThumbDownIcon, CheckCircleIcon, XCircleIcon, ClockIcon, AcademicCapIcon, BriefcaseIcon as BriefcaseSolidIcon } from "@heroicons/react/24/outline"
import { cn } from "@ui/lib/utils"
import { useScreenSize } from "@ui/providers/screenSize"
import { getLead } from "@ui/api/leads"
import { Lead, LeadMeta } from "@ui/typings/lead"
import { useAlert } from "@ui/providers/alert"
import { VoteLeadModal } from "../../VoteLeadModal"
import { VoteHistory } from "../../VoteHistory"
import { getMetaProperty,  getMetaArrayProperty,  getApolloProperty,  formatDate,  getTimeAgo,  formatRevenue,  formatGrowthPercentage,safeText, getNestedProperty} from "../../utils"

interface DetailsProps {
  leadId?: string
  token?: string
  workspaceId?: string
  onBack?: () => void
}



export const Details = ({ 
  leadId,
  token,
  workspaceId,
  onBack 
}: DetailsProps) => {
  const { isMobile } = useScreenSize()
  const { toast } = useAlert()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [leadData, setLeadData] = useState<Lead | null>(null)
  const [voteModalOpen, setVoteModalOpen] = useState(false)
  
  // Fetch lead data
  useEffect(() => {
    const fetchLeadData = async () => {
      if (!leadId || !token || !workspaceId) return
      
      setLoading(true)
      setError(null)
      
      try {
        const response = await getLead(token, workspaceId, leadId)
        
        if (response.isSuccess && response.data?.data) {
          setLeadData(response.data.data)
        } else {
          setError(response.error || "Failed to load lead data")
          toast.error("Error", {
            description: response.error || "Failed to load lead data"
          })
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "An unknown error occurred"
        setError(errorMessage)
        toast.error("Error", {
          description: errorMessage
        })
      } finally {
        setLoading(false)
      }
    }
    
    fetchLeadData()
  }, [leadId, token, workspaceId, toast])
  
  const handleVoteClick = (voteType?: 'up' | 'down') => {
    if (!leadId) {
      toast.error("Error", {
        description: "No lead selected for voting"
      })
      return
    }
    
    if (voteType) {
      // Handle quick vote without modal
      console.log(`Quick ${voteType}vote for lead ${leadId}`)
      // TODO: Implement quick voting API call
      toast.success("Success", {
        description: `Lead ${voteType}voted successfully`
      })
    } else {
      setVoteModalOpen(true)
    }
  }

  const handleVoteSuccess = () => {
    toast.success("Success", {
      description: "Your vote has been recorded"
    })
  }

  // Loading state
  if (loading) {
  return (
      <div className="w-full h-full bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-slate-600 font-medium">Loading company details...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="w-full h-full bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircleIcon className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Error loading company details</h3>
          <p className="text-red-700 mb-6">{error}</p>
          <Button variant="outline" onClick={onBack} className="border-red-300 text-red-700 hover:bg-red-50">
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  // Type guard to ensure we're working with company data
  const isCompanyLead = leadData?.type === 'company';
  const apolloCompanyData = isCompanyLead ? (leadData.apolloData as any) : null;

  // Main content
  return (
    <div className="w-full h-full bg-white overflow-auto">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-md border-b border-slate-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="p-2 hover:bg-slate-100">
              <ArrowLeftIcon className="w-5 h-5" />
            </Button>
          <div>
              <h1 className="text-xl font-bold text-slate-900">
                {apolloCompanyData?.name || leadData?.normalizedData?.name || "Company Details"}
              </h1>
              <p className="text-sm text-slate-600">
                {apolloCompanyData?.primary_domain || "Domain not specified"}
              </p>
          </div>
        </div>

          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="border-slate-300 text-slate-700 hover:bg-slate-50">
              <BriefcaseIcon className="w-4 h-4 mr-2" />
              Add to Segment
            </Button>
            <Button size="sm" className="bg-black hover:bg-gray-800 text-white">
              <EnvelopeIcon className="w-4 h-4 mr-2" />
              Unlock Email
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="w-full">
          {/* Hero Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-8 mb-6">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Company Logo & Basic Info */}
              <div className="flex flex-col lg:flex-row items-start gap-6">
                {/* Company Logo */}
                <div className="relative">
                  {apolloCompanyData?.logo_url ? (
                    <img 
                      src={String(apolloCompanyData.logo_url)} 
                      alt={apolloCompanyData?.name || "Company Logo"} 
                      className="w-24 h-24 rounded-2xl object-cover border-4 border-slate-100"
                    />
                  ) : (
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center border-4 border-slate-100">
                      <BuildingIcon className="w-6 h-6 text-white" />
                    </div>
                  )}
                  
                  {/* Company Status Indicator */}
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-3 border-white flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>

                {/* Basic Information */}
                <div className="flex-1 min-w-0">
                  <h1 className="text-2xl font-bold text-slate-900 mb-2">
                    {apolloCompanyData?.name || "Loading..."}
                  </h1>
                  
                  <p className="text-lg font-semibold text-slate-700 mb-1">
                    {apolloCompanyData?.primary_domain || "Domain not specified"}
                  </p>
                  
                  <div className="flex items-center gap-2 text-sm text-slate-600 mb-3">
                    <GlobeIcon className="w-4 h-4" />
                    <span>{apolloCompanyData?.website_url || "Website not available"}</span>
                  </div>

                  {/* Location & Founded Year */}
                  <div className="flex items-center gap-4 text-xs text-slate-500">
                    {apolloCompanyData?.founded_year && (
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="w-3 h-3" />
                        <span>Founded {apolloCompanyData.founded_year}</span>
                      </div>
                    )}
                    
                    {apolloCompanyData?.primary_domain && (
                      <div className="flex items-center gap-1">
                        <GlobeIcon className="w-3 h-3" />
                        <span>{apolloCompanyData.primary_domain}</span>
                    </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Side - Stats & Actions */}
              <div className="lg:ml-auto flex flex-col gap-4">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white rounded-xl p-4 text-center border border-slate-200">
                    <div className="text-2xl font-bold text-slate-900">
                      {formatRevenue(apolloCompanyData?.organization_revenue)}
                    </div>
                    <div className="text-xs text-slate-600 font-medium">Revenue</div>
          </div>

                  <div className="bg-white rounded-xl p-4 text-center border border-slate-200">
                    <div className="text-2xl font-bold text-slate-900">
                      {apolloCompanyData?.organization_headcount_twenty_four_month_growth ? 
                        formatGrowthPercentage(apolloCompanyData.organization_headcount_twenty_four_month_growth) : 
                        'N/A'
                      }
              </div>
                    <div className="text-xs text-slate-600 font-medium">24M Growth</div>
            </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-xs rounded-full border-slate-300 text-slate-700 hover:bg-slate-50"
                  >
                    <EnvelopeIcon className="w-3 h-3 mr-2" />
                    Send Email
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-xs rounded-full border-slate-300 text-slate-700 hover:bg-slate-50"
                  >
                    <PhoneIcon className="w-3 h-3 mr-2" />
                    Schedule Call
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-xs rounded-full border-slate-300 text-slate-700 hover:bg-slate-50"
                  >
                    <BriefcaseIcon className="w-3 h-3 mr-2" />
                    Add to Sequence
                  </Button>
                </div>
              </div>
            </div>

            {/* Company Description */}
            {apolloCompanyData?.short_description && (
              <div className="mt-6 pt-6 border-t border-slate-200">
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Company Description</h3>
                                  <p className="text-sm text-slate-600 leading-relaxed">
                  {apolloCompanyData.short_description}
                </p>
              </div>
            )}
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content - Left Column */}
            <div className="lg:col-span-2 space-y-6">
              {/* Company Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-slate-900">Company Information</h3>
                </div>
                
                {/* Company Header with Logo */}
                <div className="flex items-center gap-4 mb-6 p-4 bg-white rounded-xl border border-slate-200">
                  {apolloCompanyData?.logo_url && (
                    <img 
                      src={apolloCompanyData.logo_url} 
                      alt={`${apolloCompanyData.name || 'Company'} logo`}
                      className="w-16 h-16 rounded-lg object-cover border border-slate-200"
                    />
                  )}
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-slate-900">
                      {apolloCompanyData?.name || 'N/A'}
                    </h4>
                    {apolloCompanyData?.primary_domain && (
                      <p className="text-sm text-slate-600">
                        {apolloCompanyData.primary_domain}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                      <div className="space-y-2 text-sm text-slate-600">
                        <div className="flex items-center gap-2">
                          <BuildingIcon className="w-4 h-4 text-slate-400" />
                          <span>{String(leadData?.apolloData?.name || 'N/A')}</span>
                        </div>
                        {apolloCompanyData?.website_url && (
                          <div className="flex items-center gap-2">
                            <GlobeIcon className="w-4 h-4 text-slate-400" />
                            <a 
                              href={apolloCompanyData.website_url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-700 underline"
                            >
                              Visit Website
                            </a>
                          </div>
                        )}
                        {apolloCompanyData?.phone && (
                          <div className="flex items-center gap-2">
                            <PhoneIcon className="w-4 h-4 text-slate-400" />
                            <span>{apolloCompanyData.phone}</span>
                          </div>
                        )}
                        {apolloCompanyData?.primary_phone && (apolloCompanyData.primary_phone as any).number && (
                          <div className="flex items-center gap-2">
                            <PhoneIcon className="w-4 h-4 text-slate-400" />
                            <span className="text-sm text-slate-600">
                              {(apolloCompanyData.primary_phone as any).number}
                              <span className="text-xs text-slate-400 ml-2">
                                (Source: {(apolloCompanyData.primary_phone as any).source})
                              </span>
                            </span>
            </div>
          )}
                      </div>
        </div>

                    {/* Additional Company URLs */}
                    {(apolloCompanyData?.blog_url || apolloCompanyData?.angellist_url || apolloCompanyData?.crunchbase_url) && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Additional Links</h4>
                        <div className="space-y-2 text-sm text-slate-600">
                          {apolloCompanyData?.blog_url && (
                  <div className="flex items-center gap-2">
                              <LinkIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                href={apolloCompanyData.blog_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                Blog
                              </a>
              </div>
                          )}
                          {apolloCompanyData?.angellist_url && (
                            <div className="flex items-center gap-2">
                              <LinkIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                  href={apolloCompanyData.angellist_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                AngelList
                              </a>
            </div>
                          )}
                          {apolloCompanyData?.crunchbase_url && (
                            <div className="flex items-center gap-2">
                              <LinkIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                href={apolloCompanyData.crunchbase_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                Crunchbase
                              </a>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    </div>
                    
                  <div className="space-y-4">
                        <div>
                      <h4 className="text-sm font-semibold text-slate-700 mb-2">Growth Metrics</h4>
                      <div className="space-y-2 text-sm text-slate-600">
                        <div className="flex justify-between">
                          <span>6 Month Growth:</span>
                          <span className="font-medium">
                            {formatGrowthPercentage(apolloCompanyData?.organization_headcount_six_month_growth)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>12 Month Growth:</span>
                          <span className="font-medium">
                            {formatGrowthPercentage(apolloCompanyData?.organization_headcount_twelve_month_growth)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>24 Month Growth:</span>
                          <span className="font-medium">
                            {formatGrowthPercentage(apolloCompanyData?.organization_headcount_twenty_four_month_growth)}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                        <div>
                      <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                      <div className="space-y-2 text-sm text-slate-600">
                        {apolloCompanyData?.founded_year && (
                          <div className="flex justify-between">
                            <span>Founded:</span>
                            <span className="font-medium">
                              {safeText(apolloCompanyData.founded_year)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.primary_domain && (
                          <div className="flex justify-between">
                            <span>Domain:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.primary_domain)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.linkedin_uid && (
                          <div className="flex justify-between">
                            <span>LinkedIn ID:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.linkedin_uid)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.alexa_ranking && (
                          <div className="flex justify-between">
                            <span>Alexa Ranking:</span>
                            <span className="font-medium">
                                #{String(apolloCompanyData.alexa_ranking).toLocaleString()}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.organization_revenue && (
                          <div className="flex justify-between">
                            <span>Revenue:</span>
                            <span className="font-medium">
                              {formatRevenue(apolloCompanyData.organization_revenue)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.publicly_traded_symbol && (
                          <div className="flex justify-between">
                            <span>Public Trading:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.publicly_traded_symbol)} 
                              ({String(apolloCompanyData.publicly_traded_exchange || 'Unknown Exchange')})
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.owned_by_organization && typeof apolloCompanyData.owned_by_organization === 'object' && 'name' in apolloCompanyData.owned_by_organization && (
                          <div className="flex justify-between">
                            <span>Parent Company:</span>
                            <span className="font-medium">
                              {apolloCompanyData.owned_by_organization.name}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Company Languages */}
                    {apolloCompanyData?.languages && Array.isArray(apolloCompanyData.languages) && apolloCompanyData.languages.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Languages</h4>
                        <div className="flex flex-wrap gap-2">
                          {apolloCompanyData.languages.map((lang: any, index: number) => (
                            <span key={index} className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full font-medium">
                              {String(lang)}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {/* Intent Signals */}
                    {apolloCompanyData?.show_intent && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Intent Signals</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span>Shows Intent:</span>
                            <span className="font-medium text-green-600">
                              {apolloCompanyData.show_intent ? 'Yes' : 'No'}
                            </span>
                          </div>
                          {apolloCompanyData?.intent_strength && (
                            <div className="flex justify-between">
                              <span>Intent Strength:</span>
                              <span className="font-medium">
                                {String(apolloCompanyData.intent_strength)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Company Technologies */}
              {apolloCompanyData?.current_technologies && Array.isArray(apolloCompanyData.current_technologies) && apolloCompanyData.current_technologies.length > 0 && (
                <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Company Technologies</h3>
                  <div className="flex flex-wrap gap-2">
                    {apolloCompanyData.current_technologies.map((tech: any, index: number) => (
                      <span key={index} className="px-3 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full font-medium">
                        {String(tech)}
                          </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Media Links */}
              {(apolloCompanyData?.linkedin_url || apolloCompanyData?.twitter_url || apolloCompanyData?.facebook_url) && (
                <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Social Media</h3>
                  <div className="flex flex-wrap gap-4">
                    {apolloCompanyData?.linkedin_url && (
                      <a 
                        href={String(apolloCompanyData.linkedin_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                      >
                        <span>LinkedIn</span>
                        <ExternalLinkIcon className="w-4 h-4" />
                      </a>
                    )}
                      {apolloCompanyData?.twitter_url && (
                      <a 
                        href={String(apolloCompanyData.twitter_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                      >
                        <span>Twitter</span>
                        <ExternalLinkIcon className="w-4 h-4" />
                      </a>
                    )}
                    {apolloCompanyData?.facebook_url && (
                      <a 
                        href={String(apolloCompanyData.facebook_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                      >
                        <span>Facebook</span>
                        <ExternalLinkIcon className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Sidebar - Right Column */}
            <div className="space-y-6">
              {/* Contact Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Contact Information</h3>
                <div className="space-y-4">
                  {/* Email */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <EnvelopeIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Email</span>
                    </div>
                    {leadData?.isUnlocked && leadData.normalizedData?.isEmailVisible && leadData.normalizedData?.email ? (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">{leadData.normalizedData.email}</span>
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      </div>
                    ) : (
                      <Button variant="outline" size="sm" className="text-xs rounded-full w-full">
                        <EnvelopeIcon className="w-3 h-3 mr-2" />
                        Unlock Email
                      </Button>
                    )}
                  </div>

                  {/* Phone */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <PhoneIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Phone</span>
                    </div>
                    {leadData?.isUnlocked && leadData.normalizedData?.isPhoneVisible && leadData.normalizedData?.phone ? (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">{leadData.normalizedData.phone}</span>
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      </div>
                    ) : (
                      <Button variant="outline" size="sm" className="text-xs rounded-full w-full">
                        <PhoneIcon className="w-3 h-3 mr-2" />
                        Unlock Phone
                      </Button>
                    )}
                  </div>

                  {/* Website */}
                  {apolloCompanyData?.website_url && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <GlobeIcon className="w-4 h-4 text-slate-400" />
                        <span className="text-sm font-medium text-slate-700">Website</span>
                      </div>
                      <a 
                        href={String(apolloCompanyData.website_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
                      >
                        <span>Visit Website</span>
                        <ExternalLinkIcon className="w-3 h-3" />
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Lead Status */}
              <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Lead Status</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Status:</span>
                    <span className="text-sm font-medium text-slate-900">
                      {leadData?.isUnlocked ? 'Unlocked' : 'Locked'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Source:</span>
                    <span className="text-sm font-medium text-slate-900 capitalize">
                      {leadData?.source || 'Unknown'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Created:</span>
                    <span className="text-sm font-medium text-slate-900">
                      {leadData?.createdAt ? getTimeAgo(leadData.createdAt) : 'Unknown'}
                    </span>
                  </div>
                  {leadData?.lastEnrichedAt && (
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-600">Last Updated:</span>
                      <span className="text-sm font-medium text-slate-900">
                        {getTimeAgo(leadData.lastEnrichedAt)}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Apollo Data Insights */}
              {leadData?.apolloData && (
                <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Apollo Insights</h3>
                      <div className="space-y-3">
                    {apolloCompanyData.intent_strength && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Intent Strength:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {String(apolloCompanyData.intent_strength)}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.show_intent && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Shows Intent:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {apolloCompanyData.show_intent ? 'Yes' : 'No'}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.organization_revenue && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Revenue:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {formatRevenue(apolloCompanyData.organization_revenue)}
                        </span>
                      </div>
                    )}
                  </div>
              </div>
            )}

              {/* Vote Lead */}
              <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Rate This Company</h3>
                <p className="text-sm text-slate-600 mb-4">
                  Help improve lead quality by rating this company
                </p>

                <Button 
                  onClick={() => handleVoteClick()}
                  className="w-full bg-black hover:bg-gray-800 text-white"
                >
                  <HandThumbUpIcon className="w-4 h-4 mr-2" />
                  Rate with Feedback
                </Button>
                        </div>

              {/* Vote History */}
              {leadData?.meta?.votes && (
                <VoteHistory votes={leadData.meta.votes} />
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Vote Modal */}
      {leadId && (
      <VoteLeadModal
        open={voteModalOpen}
        onOpenChange={setVoteModalOpen}
          leadId={leadId}
        onVoteSuccess={handleVoteSuccess}
      />
      )}
    </div>
  )
}
